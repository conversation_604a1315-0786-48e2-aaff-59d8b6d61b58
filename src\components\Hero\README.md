# Hero Component

A responsive Hero component for the Invest Founders platform, built with React, TypeScript, and CSS Modules. Features a video background with SVG overlay and content section with decorative lines.

## Features

### Video Section
- **Background Video**: Autoplay, muted, looped video from `/videos/hero-video.mp4`
- **SVG Overlay**: Customizable overlay from `/images/video-filter.svg`
- **Height**: 350px (responsive: 280px on tablet, 250px on mobile)
- **Full-Width Layout**: Matches Header component's width constraints
- **Accessibility**: Proper ARIA attributes and reduced motion fallbacks

### Content Section
- **Decorative Lines**: Horizontal lines above and below content
- **Main Title**: Large, justified heading
- **Two-Column Layout**: Responsive text columns (stacked on mobile)
- **Typography**: Uses Playfair Display font with established color system

## Usage

### Basic Usage
```tsx
import { Hero } from '@/components/Hero';

export default function HomePage() {
  return (
    <div>
      <Hero />
    </div>
  );
}
```

### Custom Content
```tsx
import { Hero } from '@/components/Hero';

export default function HomePage() {
  const handleVideoLoad = () => {
    console.log('Video loaded successfully');
  };

  const handleVideoError = (error: Event) => {
    console.error('Video failed to load:', error);
  };

  return (
    <Hero
      title="Custom Hero Title"
      leftText="Custom left column text content..."
      rightText="Custom right column text content..."
      onVideoLoad={handleVideoLoad}
      onVideoError={handleVideoError}
    />
  );
}
```

### Custom Assets
```tsx
<Hero
  videoSrc="/videos/custom-hero-video.mp4"
  videoOverlayImage="/images/custom-overlay.svg"
  title="Custom Title"
  leftText="Custom left text"
  rightText="Custom right text"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | `''` | Additional CSS classes |
| `videoSrc` | `string` | `'/videos/hero-video.mp4'` | Video source path |
| `videoOverlayImage` | `string` | `'/images/video-filter.svg'` | SVG overlay path |
| `title` | `string` | Default title | Main heading text |
| `leftText` | `string` | Default text | Left column content |
| `rightText` | `string` | Default text | Right column content |
| `onVideoLoad` | `() => void` | `undefined` | Video load callback |
| `onVideoError` | `(error: Event) => void` | `undefined` | Video error callback |

## Styling

### CSS Modules
The component uses CSS Modules for styling with the following key classes:
- `.heroSection` - Main container with full-width layout
- `.videoSection` - Video background container
- `.contentSection` - Content area with decorative lines
- `.heroTitle` - Main heading styles
- `.textColumns` - Two-column responsive layout

### Color System
Uses the established color variables:
- `--color-primary-black` (#111111)
- `--color-light-gray` (#E7E7E7)
- `--color-accent-green` (#99FF99)

### Responsive Breakpoints
- **Desktop**: 1024px+ (full 350px height)
- **Tablet**: 768px-1023px (280px height, two columns)
- **Mobile**: <768px (250px height, stacked columns)

## Accessibility

### Features
- **ARIA Attributes**: Proper semantic markup
- **Reduced Motion**: Respects `prefers-reduced-motion`
- **Keyboard Navigation**: Focus states for accessibility
- **Screen Readers**: Hidden decorative elements

### Video Accessibility
- `aria-hidden="true"` on video element
- Multiple video format sources for compatibility
- Fallback background for reduced motion preference

## File Structure

```
src/components/Hero/
├── Hero.tsx              # Main Hero component
├── Hero.module.css       # CSS Modules styling
├── index.ts              # Export barrel
└── README.md             # This documentation

src/types/
└── hero.ts               # TypeScript interfaces
```

## Dependencies

- React 19+
- TypeScript 5+
- Next.js 15+ (for Image component and @/ imports)
- CSS Modules support

## Browser Support

- Modern browsers with video element support
- Fallback handling for unsupported video formats
- Progressive enhancement for older browsers
