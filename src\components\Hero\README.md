# Hero Component

A responsive Hero component for the Invest Founders platform, built with React, TypeScript, and CSS Modules. Features a video background with SVG overlay and content section with decorative lines.

## Features

### Video Section
- **Background Video**: Autoplay, muted, looped video from `/videos/hero-video.mp4`
- **SVG Overlay**: Customizable overlay from `/images/video-filter.svg`
- **Height**: 400px (responsive: 320px on tablet, 280px on mobile)
- **Contained Layout**: Centered container with responsive padding matching Header component
- **Accessibility**: Proper ARIA attributes and reduced motion fallbacks

### Content Section
- **Decorative Lines**: Horizontal lines above and below content
- **Main Title**: Large, justified heading
- **Two-Column Layout**: Responsive text columns (stacked on mobile) with optimized width utilization
- **Typography**: Uses Playfair Display font with established color system
- **Full-Width Text**: Maximizes available screen width within container boundaries

## Usage

### Basic Usage
```tsx
import { Hero } from '@/components/Hero';

export default function HomePage() {
  return (
    <div>
      <Hero />
    </div>
  );
}
```

### Custom Content
```tsx
import { Hero } from '@/components/Hero';

export default function HomePage() {
  const handleVideoLoad = () => {
    console.log('Video loaded successfully');
  };

  const handleVideoError = (error: Event) => {
    console.error('Video failed to load:', error);
  };

  return (
    <Hero
      title="Custom Hero Title"
      leftText="Custom left column text content..."
      rightText="Custom right column text content..."
      onVideoLoad={handleVideoLoad}
      onVideoError={handleVideoError}
    />
  );
}
```

### Custom Assets
```tsx
<Hero
  videoSrc="/videos/custom-hero-video.mp4"
  videoOverlayImage="/images/custom-overlay.svg"
  title="Custom Title"
  leftText="Custom left text"
  rightText="Custom right text"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | `''` | Additional CSS classes |
| `videoSrc` | `string` | `'/videos/hero-video.mp4'` | Video source path |
| `videoOverlayImage` | `string` | `'/images/video-filter.svg'` | SVG overlay path |
| `title` | `string` | Default title | Main heading text |
| `leftText` | `string` | Default text | Left column content |
| `rightText` | `string` | Default text | Right column content |
| `onVideoLoad` | `() => void` | `undefined` | Video load callback |
| `onVideoError` | `(error: Event) => void` | `undefined` | Video error callback |

## Styling

### CSS Modules
The component uses CSS Modules for styling with the following key classes:
- `.heroSection` - Main container with full-width layout
- `.videoSection` - Video background container
- `.contentSection` - Content area with decorative lines
- `.heroTitle` - Main heading styles
- `.textColumns` - Two-column responsive layout

### Color System
Uses the established color variables:
- `--color-primary-black` (#111111)
- `--color-light-gray` (#E7E7E7)
- `--color-accent-green` (#99FF99)

### Responsive Breakpoints
- **Desktop**: 1024px+ (full 400px height, 4rem gap between columns)
- **Tablet**: 768px-1023px (320px height, 3rem gap, two columns)
- **Mobile**: <768px (280px height, 1.5rem gap, stacked columns)
- **Small Mobile**: <480px (280px height, 1rem gap, stacked columns)

## Accessibility

### Features
- **ARIA Attributes**: Proper semantic markup
- **Reduced Motion**: Respects `prefers-reduced-motion`
- **Keyboard Navigation**: Focus states for accessibility
- **Screen Readers**: Hidden decorative elements

### Video Accessibility
- `aria-hidden="true"` on video element
- Multiple video format sources for compatibility
- Fallback background for reduced motion preference

## File Structure

```
src/components/Hero/
├── Hero.tsx              # Main Hero component
├── Hero.module.css       # CSS Modules styling
├── index.ts              # Export barrel
└── README.md             # This documentation

src/types/
└── hero.ts               # TypeScript interfaces
```

## Dependencies

- React 19+
- TypeScript 5+
- Next.js 15+ (for Image component and @/ imports)
- CSS Modules support

## Browser Support

- Modern browsers with video element support
- Fallback handling for unsupported video formats
- Progressive enhancement for older browsers
